// vite.config.ts
import { defineConfig } from "file:///C:/Projects/tracker-app/client/node_modules/vite/dist/node/index.js";
import react from "file:///C:/Projects/tracker-app/client/node_modules/@vitejs/plugin-react/dist/index.mjs";
import mkcert from "file:///C:/Projects/tracker-app/client/node_modules/vite-plugin-mkcert/dist/mkcert.mjs";
import eslint from "file:///C:/Projects/tracker-app/client/node_modules/vite-plugin-eslint/dist/index.mjs";
import csp from "file:///C:/Projects/tracker-app/client/node_modules/vite-plugin-csp/dist/index.js";
import stylelint from "file:///C:/Projects/tracker-app/client/node_modules/vite-plugin-stylelint/dist/index.mjs";
import svgr from "file:///C:/Projects/tracker-app/client/node_modules/vite-plugin-svgr/dist/index.js";
import gci from "file:///C:/Projects/tracker-app/client/node_modules/git-commit-info/dest/index.js";
import { viteStaticCopy } from "file:///C:/Projects/tracker-app/client/node_modules/vite-plugin-static-copy/dist/index.js";
import fs from "fs";
import _ from "file:///C:/Projects/tracker-app/client/node_modules/lodash/lodash.js";
import path from "path";
import { createRequire } from "module";

// configWhitelist.json
var configWhitelist_default = [
  "nodeEnv",
  "apiRoot",
  "switchAppRoute",
  "loginRoute",
  "graphQLEndpoint",
  "useOAuthGrant",
  "sentryDSN",
  "sentry",
  "veritoneAppId",
  "aiwareJSPath",
  "aiwareJSVersion",
  "enableTrackletsIntersection"
];

// vite.config.ts
var __vite_injected_original_dirname = "C:\\Projects\\tracker-app\\client";
var __vite_injected_original_import_meta_url = "file:///C:/Projects/tracker-app/client/vite.config.ts";
var require2 = createRequire(__vite_injected_original_import_meta_url);
var processEnv = process.env.ENVIRONMENT || "local";
var deployedConfigPath = `./config-${processEnv}.json`;
var localConfigPath = "./config.json";
var isDeployed = processEnv && fs.existsSync(deployedConfigPath);
var appConfig = require2(isDeployed ? deployedConfigPath : localConfigPath);
var safeConfig = _.pick(appConfig, configWhitelist_default);
console.log(`Node version ${process.versions.node}`);
var vite_config_default = defineConfig({
  assetsInclude: ["**/*.css"],
  resolve: {
    alias: {
      // fs: require.resolve('rollup-plugin-node-builtins'),
      // acorn: require.resolve('rollup-plugin-node-builtins'),
      "@common-modules": path.resolve(__vite_injected_original_dirname, "./src/common-modules"),
      "@components": path.resolve(__vite_injected_original_dirname, "./src/components"),
      "@helpers": path.resolve(__vite_injected_original_dirname, "./src/helpers"),
      "@i18n": path.resolve(__vite_injected_original_dirname, "./src/i18n"),
      "@store": path.resolve(__vite_injected_original_dirname, "./src/store"),
      "@theme": path.resolve(__vite_injected_original_dirname, "./src/theme"),
      "@pages": path.resolve(__vite_injected_original_dirname, "./src/pages"),
      "@utility": path.resolve(__vite_injected_original_dirname, "./src/utility"),
      "@shared-types": path.resolve(__vite_injected_original_dirname, "../types/*"),
      "@shared-assets": path.resolve(__vite_injected_original_dirname, "../assets")
    }
  },
  plugins: [
    react(),
    eslint(),
    stylelint(),
    mkcert({ hosts: ["local.veritone.com"] }),
    svgr(),
    csp({
      policy: {
        "base-uri": ["self"],
        "object-src": ["none"],
        "script-src": ["self", "unsafe-eval", "get.aiware.com", "veritone.my.site.com", "cdn.jsdelivr.net", "cdnjs.cloudflare.com", "nonce-NGINX_CSP_NONCE", "*.AIWARE_DOMAIN_TO_REPLACE"],
        "style-src": ["self", "fonts.googleapis.com", "unsafe-inline", "cdn.jsdelivr.net", "static.veritone.com", "veritone.my.site.com", "*.AIWARE_DOMAIN_TO_REPLACE"],
        "font-src": ["self", "data:", "fonts.googleapis.com", "cdn.jsdelivr.net", "fonts.gstatic.com", "static.veritone.com", "*.AIWARE_DOMAIN_TO_REPLACE"],
        "frame-ancestors": ["self", "support.veritone.com", "*.AIWARE_DOMAIN_TO_REPLACE"],
        "frame-src": ["self", "*.AIWARE_DOMAIN_TO_REPLACE"],
        "worker-src": ["self", "blob:", "*.AIWARE_DOMAIN_TO_REPLACE"]
      },
      hashingMethod: "sha256",
      hashEnabled: {
        "script-src": false,
        "style-src": false,
        "script-src-attr": false,
        "style-src-attr": false
      },
      nonceEnabled: {
        "script-src": false,
        "style-src": false
      }
    }),
    viteStaticCopy({
      targets: [
        {
          src: "../assets/fonts/*",
          dest: "fonts"
        }
      ]
    })
  ],
  server: {
    host: "local.veritone.com",
    port: 3004
  },
  define: {
    buildDetails: {
      hash: gci().hash,
      date: gci().date,
      message: gci().message
    },
    config: JSON.stringify(safeConfig)
  },
  html: {
    cspNonce: "NGINX_CSP_NONCE"
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "./src/theme/global.scss";`
      }
    }
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
