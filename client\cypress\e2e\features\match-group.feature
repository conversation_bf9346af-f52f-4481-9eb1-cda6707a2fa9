Feature: Match Group

  Background:
    Given The user is on Event Screen

  @e2e @match-group
  Scenario: Check UI of Match Group
    Given The user navigates to "Match Groups" tab of event name "lucy" through search
    And The user clicks "View Match Group" for the first match group
    And The user clicks the "Search Results" tab in the match group page
    And The user selects "Potential Match Search 1" from the dropdown
    Then The user should see the match group page UI
    And The user should see the following match group UI elements:
      | Element Type       | Expected Value                     |
      | File Name Dropdown | visible                            |
      | Export Button      | visible                            |
      | Left Panel Text    | Select a Detection to View Details |
      | Tab 1              | Search Results                     |
      | Tab 2              | Verified Matches                   |
      | Tab 3              | Timeline Editor                    |
      | Default Tab        | Search Results                     |

  @e2e @match-group
  Scenario: Verify user can change name for match group search
    Then The user deletes the following Match search Groups if exist:
      | searchName              |
      | e2e-search-name         |
      | e2e-updated-search-name |
    Given Create a new Search Match Group named "e2e-search-name" with attributes
      | group  | label     | value | key  |
      | person | FaceFront | Front | Face |
    Given The user navigates to "Match Groups" tab of event name "lucy" through search
    And The user expands the first match group named "E2E-Match-Group"
    When The user hovers on a match group search named "e2e-search-name"
    And The user clicks on the edit icon for the search named "e2e-search-name"
    And The user enters "e2e-updated-search-name" as the new search name
    And The user clicks "Save" in the search dialog
    Then The user should see the notification message "Updated E2E-Match-Group search name successfully"

  @e2e @match-group
  Scenario: Verify user can delete a match group search
    Then The user deletes the following Match search Groups if exist:
      | searchName        |
      | e2e-search-delete |
    Given Create a new Search Match Group named "e2e-search-delete" with attributes
      | group  | label     | value | key  |
      | person | FaceFront | Front | Face |
    Given The user navigates to "Match Groups" tab of event name "lucy" through search
    And The user expands the first match group named "E2E-Match-Group"
    When The user hovers on a match group search named "e2e-search-delete"
    And The user clicks on the delete icon for the search named "e2e-search-delete"
    Then The user should see the delete confirmation modal
    When The user clicks the "Delete" button in the dialog
    Then The user should see the notification message "Match Group Search successfully deleted"

  @e2e @match-group
  Scenario: Verify user can edit match group name
    Given The user deletes the following Match Groups Name if exist:
      | matchGroupName          |
      | E2E-Edit-Match-Group    |
      | E2E-updated-match-group |
    Then The user creates Match Group Name "E2E-Edit-Match-Group" through API
    Given The user navigates to "Match Groups" tab of event name "lucy" through search
    When The user clicks on the 3 dots button for match group named "E2E-Edit-Match-Group"
    And The user selects "Rename"
    And The user enters "e2e-updated-match-group" as the new name
    And The user clicks "Save" in the match group dialog
    Then The match group name should be updated to "e2e-updated-match-group"

  @e2e @match-group
  Scenario: Verify user can delete match group
    Given The user deletes the following Match Groups Name if exist:
      | matchGroupName         |
      | E2E-Delete-Match-Group |
    Then The user creates Match Group Name "E2E-Delete-Match-Group" through API
    Given The user navigates to "Match Groups" tab of event name "lucy" through search
    When The user clicks on the 3 dots button for match group named "E2E-Delete-Match-Group"
    And The user selects "Delete"
    When The user clicks the "Delete" button in the dialog
    Then The user should see the notification message "Delete match-group success"

  @e2e @match-group @search-results
  Scenario: Verify user can View Search in a match group
    Given The user navigates to "Match Groups" tab of event name "lucy" through search
    And The user expands the first match group named "E2E-Match-Group"
    And The user clicks the "View Search" button for the first search
    Then The user should navigate to the match search view page
    And The user should see that the page has finished loading

  @e2e @match-group @search-results
  Scenario: Verify UI of Search Results tab
    Given The user navigates to "Match Groups" tab of event name "lucy" through search
    And The user expands the first match group named "E2E-Match-Group"
    And The user clicks the "View Search" button for the first search
    Then The user should navigate to the match search view page
    And The user should see that the page has finished loading
    And The user should see the following elements on the Search Results page:
      | Element Type                    | Expected State |
      | Breadcrumb                      | visible        |
      | File Name Dropdown              | visible        |
      | Mark as Verified Button         | disabled       |
      | Find Matches Button             | disabled       |
      | Potential Match Search Dropdown | visible        |
      | Media Player Default Text       | visible        |
      | Select All Checkbox             | visible        |
      | Confidence Slider               | visible        |
      | Show Match Score Checkbox       | visible        |
      | Pagination                      | visible        |
      | Thumbnail Scaler                | visible        |

  @e2e @match-group @search-results
  Scenario: Verify user can run Find Matches for a tracklet
    Given The user navigates to "Match Groups" tab of event name "lucy" through search
    And The user expands the first match group named "E2E-Match-Group"
    And The user clicks the "View Search" button for the first search
    Then The user should navigate to the match search view page
    And The user should see that the page has finished loading
    When The user selects the "1st" tracklet
    And The user clicks the "Find Matches" button in the match group page
    Then The "Find Matches" popover should be visible
    When The user clicks the "New Match Group" button in the popover
    And The user enters "e2e-find-matches-group" as the new match group name
    And The user clicks the "Create" button for the new match group
    Then The user should see the success notification "Match Group e2e-find-matches-group was created successfully"
    And The user clicks the "Continue" button in the popover
    When The user clicks the "lucy" breadcrumb
    When The user clicks the "Match Groups" tab
    Then The match group named "e2e-find-matches-group" should be visible
    When The user clicks the 3 dots menu for the "2nd" match group
    And The user selects "Delete"
    When The user clicks the "Yes, Delete" button in the dialog

  @e2e @match-group @search-results
  Scenario: Verify UI when user clicks on a tracklet
    Given The user navigates to "Match Groups" tab of event name "lucy" through search
    And The user expands the first match group named "E2E-Match-Group"
    And The user clicks the "View Search" button for the first search
    Then The user should navigate to the match search view page
    And The user should see that the page has finished loading
    When The user clicks on the "1st" tracklet
    Then The "Detected Attributes" accordion should be visible
    And The "File Metadata" accordion should be visible

  @e2e @match-group @search-results
  Scenario: Verify tracklet UI when its checkbox is selected
    Given The user navigates to "Match Groups" tab of event name "lucy" through search
    And The user expands the first match group named "E2E-Match-Group"
    And The user clicks the "View Search" button for the first search
    Then The user should navigate to the match search view page
    And The user should see that the page has finished loading
    When The user selects the checkbox for the "1st" tracklet
    Then The "1st" tracklet should have a blue outline

  @e2e @match-group @search-results
  Scenario: Verify video play and pause functionality
    Given The user navigates to "Match Groups" tab of event name "lucy" through search
    And The user expands the first match group named "E2E-Match-Group"
    And The user clicks the "View Search" button for the first search
    Then The user should navigate to the match search view page
    And The user should see that the page has finished loading
    When The user clicks on the "1st" tracklet
    Then The video player state should be "paused"
    When The user clicks the video player "Play" button
    Then The video player state should be "playing"
    When The user clicks the video player "Pause" button
    Then The video player state should be "paused"

  @e2e @match-group @search-results @skip
  Scenario: Verify user can add a tracklet to the match group that owns the current potential match search

  @e2e @match-group @search-results
  Scenario: Verify the confidence slider is visible on the Search Results page
    Given The user navigates to "Match Groups" tab of event name "lucy" through search
    And The user expands the first match group named "E2E-Match-Group"
    And The user clicks the "View Search" button for the first search
    Then The user should navigate to the match search view page
    And The user should see that the page has finished loading
    Then the confidence slider should be visible

  @e2e @match-group @search-results @skip
  Scenario: Verify user cannot adjust the slider to view more matches with Attribute Match Search

  @e2e @match-group @search-results
  Scenario: Verify user can select all matches on the page
    Given The user navigates to "Match Groups" tab of event name "lucy" through search
    And The user expands the first match group named "E2E-Match-Group"
    And The user clicks the "View Search" button for the first search
    Then The user should navigate to the match search view page
    And The user should see that the page has finished loading
    When The user clicks the select all checkbox
    Then all tracklets on the page should be selected

  @e2e @match-group @search-results
  Scenario: Verify user can enable and view match scores
    Given The user navigates to "Match Groups" tab of event name "lucy" through search
    And The user expands the first match group named "E2E-Match-Group"
    And The user clicks the "View Search" button for the first search
    Then The user should navigate to the match search view page
    And The user should see that the page has finished loading
    When The user clicks the "Show Match Score" checkbox
    Then all visible tracklets should display a match score

  @e2e @match-group @search-results
  Scenario: Verify user can only find matches for one tracklet at a time
    Given The user navigates to "Match Groups" tab of event name "lucy" through search
    And The user expands the first match group named "E2E-Match-Group"
    And The user clicks the "View Search" button for the first search
    Then The user should navigate to the match search view page
    And The user should see that the page has finished loading
    When The user selects the checkbox for the "1st" tracklet
    Then The "Find Matches" button should be "enabled"
    When The user selects the checkbox for the "2nd" tracklet
    Then The "Find Matches" button should be "disabled"

  @e2e @match-group @search-results
  Scenario: Verify user can filter tracklets by file
    Given The user navigates to "Match Groups" tab of event name "lucy" through search
    And The user expands the first match group named "E2E-Match-Group"
    And The user clicks the "View Search" button for the first search
    Then The user should navigate to the match search view page
    And The user should see that the page has finished loading
    When The user filters by the file named "lucy.mp4"
    Then all visible tracklets should be from the same source file

  @e2e @match-group @search-results
  Scenario: Verify user can switch between potential match searches
    Given The user navigates to "Match Groups" tab of event name "lucy" through search
    And The user expands the first match group named "E2E-Match-Group"
    And The user clicks the "View Search" button for the first search
    Then The user should navigate to the match search view page
    And The user should see that the page has finished loading
    When The user selects "Potential Match Search 2" from the potential match search dropdown
    Then "Potential Match Search 2" should be the selected search

  @e2e @match-group
  Scenario: Verify user can open the Attribute Search modal
    Given The user navigates to "Match Groups" tab of event name "lucy" through search
    When The user clicks the "Search All Files in this Event" button
    And The modal should contain "People" and "Vehicles" options
    And The modal should contain "Search" and "Cancel" buttons

  @e2e @match-group
  Scenario: Verify attribute match search is updated after user makes changes
    Given The user navigates to "Match Groups" tab of event name "lucy" through search
    And The user clicks the "Search All Files in this Event" button
    When The user clicks the "Search People" button
    Then The user should see the notification message "Please select an attribute to search"
    When The user selects the "Body" attribute
    And The user selects the "Thin" attribute
    And The user selects the "E2E-Match-Group" match group
    And The user clicks the "Search People" button
    Then The user should see the notification message "Match Group E2E-Match-Group was updated successfully"

  @e2e @match-group
  Scenario: Verify user can delete an attribute match search
    Given The user navigates to "Match Groups" tab of event name "lucy" through search
    And The user expands the first match group named "E2E-Match-Group"
    When The user clicks the delete icon for the "Attribute Search 2"
    And The user clicks the "Delete" button in the dialog
    Then The user should see the notification message "Match Group Search successfully deleted"
    And The "Attribute Search 2" should no longer be visible

  @e2e @match-group @search-results
  Scenario: Verify user can use all widgets in video preview
    Given The user navigates to "Match Groups" tab of event name "lucy" through search
    And The user expands the first match group named "E2E-Match-Group"
    And The user clicks the "View Search" button for the first search
    Then The user should navigate to the match search view page
    And The user should see that the page has finished loading
    When The user clicks on the "1st" tracklet
    When The user clicks the video player "Play" button
    Then The video player state should be "playing"
    When The user clicks the "replay 5 seconds" widget button
    And The user clicks the "forward 5 seconds" widget button
    And The user seeks to the middle of the video progress bar
    And The user changes the video playback speed to "1.5x"
    Then The video playback speed should be "1.50x"
    When The user clicks the "Fullscreen" button
    Then The video player should be in fullscreen mode

  @e2e @match-group @search-results
  Scenario: Verify user can switch between 2 accordion summaries
    Given The user navigates to "Match Groups" tab of event name "lucy" through search
    And The user expands the first match group named "E2E-Match-Group"
    And The user clicks the "View Search" button for the first search
    Then The user should navigate to the match search view page
    And The user should see that the page has finished loading
    When The user clicks on the "1st" tracklet
    Then The "Detected Attributes" accordion should be "expanded"
    And The "File Metadata" accordion should be "collapsed"
    When The user clicks the "File Metadata" accordion header
    Then The "Detected Attributes" accordion should be "collapsed"
    And The "File Metadata" accordion should be "expanded"

  @e2e @match-group @search-results
  Scenario: Verify user can close both accordion summaries
    Given The user navigates to "Match Groups" tab of event name "lucy" through search
    And The user expands the first match group named "E2E-Match-Group"
    And The user clicks the "View Search" button for the first search
    Then The user should navigate to the match search view page
    And The user should see that the page has finished loading
    When The user clicks on the "1st" tracklet
    Then The "Detected Attributes" accordion should be "expanded"
    When The user clicks the "Detected Attributes" accordion header
    Then The "Detected Attributes" accordion should be "collapsed"
    And The "File Metadata" accordion should be "collapsed"

  @e2e @match-group @search-results
  Scenario: Verify File Metadata is displayed correctly
    Given The user navigates to "Match Groups" tab of event name "lucy" through search
    And The user expands the first match group named "E2E-Match-Group"
    And The user clicks the "View Search" button for the first search
    Then The user should navigate to the match search view page
    And The user should see that the page has finished loading
    When The user clicks on the "1st" tracklet
    And The user clicks the "File Metadata" accordion header
    Then The following file metadata should be visible:
      | Label             | Value               |
      | File Name         | lucy.mp4            |
      | Upload Date       | 8/22/2025, 11:00 AM |
      | File GPS Location | unavailable         |
      | File Type         | video/mp4           |
      | File Size         |              1.6 Mb |
      | Video Length      |            00:00:04 |

  @e2e @match-group @search-results
  Scenario: Verify the thumbnail scaler widget is visible
    Given The user navigates to "Match Groups" tab of event name "lucy" through search
    And The user expands the first match group named "E2E-Match-Group"
    And The user clicks the "View Search" button for the first search
    Then The user should navigate to the match search view page
    And The user should see that the page has finished loading
    Then The thumbnail scaler widget should be visible

  @e2e @match-group @search-results
  Scenario: Verify user can change the number of results per page
    Given The user navigates to "Match Groups" tab of event name "lucy" through search
    And The user expands the first match group named "E2E-Match-Group"
    And The user clicks the "View Search" button for the first search
    Then The user should navigate to the match search view page
    And The user should see that the page has finished loading
    When The user changes the results per page to "10"
    Then the "Results Per Page" value should be "10"
    When The user changes the results per page to "50"
    Then the "Results Per Page" value should be "50"

  @e2e @match-group @search-results
  Scenario: Verify user can move between pages
    Given The user navigates to "Match Groups" tab of event name "lucy" through search
    And The user expands the "E2E-Match-Group" match group
    And The user clicks "View Search" for the "E2E Attribute Search"
    Then The user should navigate to the match search view page
    And The user should see that the page has finished loading
    When The user changes the results per page to "10"
    Then The current page number should be "1"
    When The user clicks the "Next Page" pagination control
    Then The current page number should be "2"
    When The user clicks the "Previous Page" pagination control
    Then The current page number should be "1"
